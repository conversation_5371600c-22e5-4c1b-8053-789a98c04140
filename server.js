const express = require('express');
const OpenAI = require('openai');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'OpenAI API Key Tester',
    version: '1.0.0',
    endpoints: {
      'POST /test-key': 'Test an OpenAI API key',
      'GET /': 'This health check endpoint'
    }
  });
});

// Test OpenAI API key endpoint
app.post('/test-key', async (req, res) => {
  try {
    const  apiKey  = process.env.OPENAI_API_KEY;

    // Validate input
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API key is required',
        message: 'Please provide an OpenAI API key in the request body'
      });
    }

    // Validate API key format
    if (!apiKey.startsWith('sk-')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid API key format',
        message: 'OpenAI API keys should start with "sk-"'
      });
    }

    // Initialize OpenAI client with the provided key
    const openai = new OpenAI({
      apiKey: apiKey,
    });

    // Test the API key by making a simple request
    const startTime = Date.now();
    
    try {
      // Make a minimal request to test the key
      const response = await openai.models.list();
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Check if we got a valid response
      if (response && response.data && Array.isArray(response.data)) {
        const modelCount = response.data.length;
        const availableModels = response.data
          .slice(0, 5) // Show first 5 models
          .map(model => model.id);

        return res.json({
          success: true,
          message: 'API key is valid and working',
          details: {
            responseTime: `${responseTime}ms`,
            modelCount: modelCount,
            sampleModels: availableModels,
            keyPrefix: apiKey.substring(0, 7) + '...'
          }
        });
      } else {
        return res.status(500).json({
          success: false,
          error: 'Unexpected response format',
          message: 'The API key appears to be valid but returned an unexpected response'
        });
      }

    } catch (openaiError) {
      // Handle specific OpenAI API errors
      if (openaiError.status === 401) {
        return res.status(401).json({
          success: false,
          error: 'Invalid API key',
          message: 'The provided API key is not valid or has been revoked'
        });
      } else if (openaiError.status === 429) {
        return res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          message: 'The API key has exceeded its rate limit'
        });
      } else if (openaiError.status === 403) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          message: 'The API key does not have sufficient permissions'
        });
      } else {
        return res.status(500).json({
          success: false,
          error: 'OpenAI API error',
          message: openaiError.message || 'An error occurred while testing the API key'
        });
      }
    }

  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error',
      message: 'An internal server error occurred'
    });
  }
});

// Test with a simple completion endpoint
app.post('/test-completion', async (req, res) => {
  try {
    const { apiKey, model = 'gpt-3.5-turbo', prompt = 'Say hello!' } = req.body;

    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API key is required'
      });
    }

    const openai = new OpenAI({
      apiKey: apiKey,
    });

    const startTime = Date.now();
    
    const completion = await openai.chat.completions.create({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 50
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    return res.json({
      success: true,
      message: 'Completion test successful',
      details: {
        responseTime: `${responseTime}ms`,
        model: completion.model,
        usage: completion.usage,
        response: completion.choices[0].message.content
      }
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Completion test failed',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: 'Something went wrong!'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    message: 'The requested endpoint does not exist'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 OpenAI API Key Tester running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/`);
  console.log(`🔑 Test endpoint: POST http://localhost:${PORT}/test-key`);
  console.log(`💬 Completion test: POST http://localhost:${PORT}/test-completion`);
});

module.exports = app;
